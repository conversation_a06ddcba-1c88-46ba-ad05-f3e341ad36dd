import siteinfo from '../../siteinfo.js';
import serviceApi from '../../api/modules/service.js';

Page({
  data: {
    userInfo: null,
    siteinfo,
    pets: null,
    typesList: [],
    typeId: null,
    currentTabCode: null,
    currentType: {},
    serviceList: [],
  },

  onLoad(options) {
    const { data } = options;
    this.setData({
      currentTabCode: data,
    });

    // 如果有传入的服务类型代码，立即加载对应的服务类型列表
    if (data) {
      this.getTypeList(data);
    }
  },

  onShow() {
    const item = wx.getStorageSync('selectPetInfo');
    this.setData({
      pets: item,
    });

    // 如果还没有加载过服务类型列表，且有当前标签代码，则加载
    if (this.data.typesList.length === 0 && this.data.currentTabCode) {
      this.getTypeList(this.data.currentTabCode);
    }
  },

  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
    };
  },

  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
    };
  },

  addCurrentPet() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/service/pet/index?isService=true',
    });
  },

  // 监听导航栏切换事件
  handleTabChange(e) {
    const { code } = e.detail;

    // 更新当前标签代码
    this.setData({
      currentTabCode: code
    });

    // 如果代码有效，获取类型列表
    if (code) {
      this.getTypeList(code);
    } else {
      console.warn('handleTabChange: 接收到空的 code 参数');
    }
  },

  // 获取一级类目
  getTypeList(type) {
    if (!type) {
      console.warn('getTypeList: type 参数为空');
      return;
    }

    serviceApi.list(type).then(res => {
      const typesList = (res.list || []).map(item => ({
        id: item.id,
        avatar: item.avatar,
        type: item.name.slice(0, -2),
        subtype: item.name.slice(-2),
      }));

      this.setData({
        typesList,
        typeId: typesList[0]?.id || null,
        currentType: typesList[0] || {},
      });

      // 只有当有有效的类型ID时才获取服务列表
      const firstTypeId = typesList[0]?.id;
      if (firstTypeId) {
        this.getServiceList(firstTypeId);
      } else {
        console.warn('getTypeList: 没有可用的服务类型');
        this.setData({
          serviceList: [],
        });
      }
    }).catch(error => {
      console.error('获取服务类型列表失败:', error);
      this.setData({
        typesList: [],
        typeId: null,
        currentType: {},
        serviceList: [],
      });
    });
  },

  // 一级类目切换
  changeType(evt) {
    let { id } = evt.currentTarget.dataset;
    if (id == this.data.typeId) return;
    this.setData({
      typeId: id,
      currentType: this.data.typesList.find(item => item.id == id) || {},
    });
    this.getServiceList(id);
  },

  // 获取服务列表
  getServiceList(typeId) {
    // 如果 typeId 为空，清空服务列表并返回
    if (!typeId) {
      console.warn('getServiceList: typeId 为空，无法获取服务列表');
      this.setData({
        serviceList: [],
      });
      return;
    }

    const { type, hairType, weightType } = this.data.pets || {};
    let petInfo = {};
    if (type) {
      petInfo['type'] = type;
    }
    if (hairType) {
      petInfo['hairType'] = hairType;
    }
    if (weightType) {
      petInfo['weightType'] = weightType;
    }

    serviceApi.services(typeId, petInfo).then(list => {
      this.setData({
        serviceList: list || [],
      });
    }).catch(error => {
      console.error('获取服务列表失败:', error);
      this.setData({
        serviceList: [],
      });
    });
  },

  redirect(evt) {
    let { item } = evt.currentTarget.dataset;
    wx.setStorageSync('selectServiceInfo', {
      ...item,
      currentType: this.data.currentType,
    });
    wx.navigateTo({
      url: '/pages/service/reservation/index?serviceId=' + item.id,
    });
  },
});
