# 服务页面 typeId 为 null 问题修复

## 问题描述

在 `pages/service/index.js` 中，调用 `serviceApi.services(typeId, petInfo)` 时出现 `typeId` 为 `null` 的情况，导致 API 调用失败。

## 问题分析

### 根本原因

1. **页面生命周期时序问题**：
   - 页面 `onLoad` 时设置了 `currentTabCode`
   - `custom-navbar` 组件在 `attached` 生命周期中立即调用 `getCategories()`
   - 组件获取数据后立即触发 `tabchange` 事件
   - 此时页面可能还没有完全初始化完成

2. **数据初始化不完整**：
   - 页面没有在 `onLoad` 时主动获取服务类型列表
   - 完全依赖组件的事件触发来初始化数据
   - 当组件事件触发时，可能获取不到有效的服务类型数据

3. **错误处理不足**：
   - `getServiceList` 方法没有检查 `typeId` 是否有效
   - 没有对 API 调用失败的情况进行处理

## 解决方案

### 1. 页面初始化优化

**修改文件**: `pages/service/index.js`

```javascript
onLoad(options) {
  const { data } = options;
  this.setData({
    currentTabCode: data,
  });
  
  // 如果有传入的服务类型代码，立即加载对应的服务类型列表
  if (data) {
    this.getTypeList(data);
  }
},

onShow() {
  const item = wx.getStorageSync('selectPetInfo');
  this.setData({
    pets: item,
  });
  
  // 如果还没有加载过服务类型列表，且有当前标签代码，则加载
  if (this.data.typesList.length === 0 && this.data.currentTabCode) {
    this.getTypeList(this.data.currentTabCode);
  }
},
```

### 2. 事件处理增强

```javascript
// 监听导航栏切换事件
handleTabChange(e) {
  const { code } = e.detail;
  
  // 更新当前标签代码
  this.setData({
    currentTabCode: code
  });
  
  // 如果代码有效，获取类型列表
  if (code) {
    this.getTypeList(code);
  } else {
    console.warn('handleTabChange: 接收到空的 code 参数');
  }
},
```

### 3. API 调用防护

```javascript
// 获取一级类目
getTypeList(type) {
  if (!type) {
    console.warn('getTypeList: type 参数为空');
    return;
  }
  
  serviceApi.list(type).then(res => {
    const typesList = (res.list || []).map(item => ({
      id: item.id,
      avatar: item.avatar,
      type: item.name.slice(0, -2),
      subtype: item.name.slice(-2),
    }));
    
    this.setData({
      typesList,
      typeId: typesList[0]?.id || null,
      currentType: typesList[0] || {},
    });
    
    // 只有当有有效的类型ID时才获取服务列表
    const firstTypeId = typesList[0]?.id;
    if (firstTypeId) {
      this.getServiceList(firstTypeId);
    } else {
      console.warn('getTypeList: 没有可用的服务类型');
      this.setData({
        serviceList: [],
      });
    }
  }).catch(error => {
    console.error('获取服务类型列表失败:', error);
    this.setData({
      typesList: [],
      typeId: null,
      currentType: {},
      serviceList: [],
    });
  });
},

// 获取服务列表
getServiceList(typeId) {
  // 如果 typeId 为空，清空服务列表并返回
  if (!typeId) {
    console.warn('getServiceList: typeId 为空，无法获取服务列表');
    this.setData({
      serviceList: [],
    });
    return;
  }
  
  const { type, hairType, weightType } = this.data.pets || {};
  let petInfo = {};
  if (type) {
    petInfo['type'] = type;
  }
  if (hairType) {
    petInfo['hairType'] = hairType;
  }
  if (weightType) {
    petInfo['weightType'] = weightType;
  }
  
  serviceApi.services(typeId, petInfo).then(list => {
    this.setData({
      serviceList: list || [],
    });
  }).catch(error => {
    console.error('获取服务列表失败:', error);
    this.setData({
      serviceList: [],
    });
  });
},
```

### 4. 组件优化

**修改文件**: `components/custom-navbar/custom-navbar.js`

```javascript
getCategories() {
  dictionaryApi.list("服务类型").then((list) => {
    const tabsDatas = list.map((item) => ({
      text: "约" + item.name,
      code: item.code,
    }));
    
    // 设置数据并默认选中传递的数据或第一项
    const code = this.properties.currentCode || this.data.currentCode || tabsDatas[0]?.code;
    
    this.setData({
      tabsDatas,
      currentCode: code || "",
    });
    
    // 只有当有有效的 code 时才触发事件
    if (code) {
      this.triggerEvent("tabchange", {
        code: code
      });
    } else {
      console.warn('custom-navbar: 没有有效的服务类型代码');
    }
  }).catch(error => {
    console.error('获取服务类型失败:', error);
  });
},
```

## 修复效果

1. **防止 null 值传递**: 在调用 API 前检查参数有效性
2. **增强错误处理**: 添加 catch 处理和日志输出
3. **优化初始化流程**: 页面主动初始化数据，不完全依赖组件事件
4. **提高代码健壮性**: 多层防护确保程序稳定运行

## 测试建议

1. **正常流程测试**: 验证页面正常加载和切换功能
2. **异常情况测试**: 模拟网络错误、数据为空等情况
3. **生命周期测试**: 验证页面切换、返回等场景下的数据状态
4. **组件交互测试**: 验证导航栏组件与页面的事件交互

## 预防措施

1. **参数验证**: 所有 API 调用前都应验证参数有效性
2. **错误处理**: 为所有异步操作添加错误处理
3. **状态管理**: 明确数据初始化的时机和顺序
4. **日志记录**: 添加适当的日志输出便于调试
